export enum Role {
  ADMIN = "admin",
  USER = "user",
}

export enum AppType {
  PASSWORD_MANAGERS = "password_managers",
  MESSAGING_CALLING = "messaging_calling",
  IDENTITY_SSO = "identity_sso",
  EMAIL_CALENDARS = "email_calendars",
  CLOUD_INFRASTRUCTURE = "cloud_infrastructure",
  DEV_COLLABORATION = "dev_collaboration",
  SOCIAL_NETWORKING = "social_networking",
  OTP_APPS = "otp_apps",
}

export const APP_TYPE_TEXT = {
  [AppType.PASSWORD_MANAGERS]: "🔑 Password & Secrets Managers",
  [AppType.MESSAGING_CALLING]: "📲 Messaging & Calling",
  [AppType.IDENTITY_SSO]: "🆔 Identity & SSO",
  [AppType.EMAIL_CALENDARS]: "✉️ Email & Calendars",
  [AppType.CLOUD_INFRASTRUCTURE]: "☁️ Cloud & Infrastructure",
  [AppType.DEV_COLLABORATION]: "💼 Dev & Collaboration",
  [AppType.SOCIAL_NETWORKING]: "🤝 Social & Networking",
  [AppType.OTP_APPS]: "🔐 OTP Apps",
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const APP_TYPE_COLORS: Record<AppType, any> = {
  [AppType.PASSWORD_MANAGERS]: {
    primary: "#3b82f6",
    secondary: "#1e40af",
    accent: "#60a5fa",
  },
  [AppType.MESSAGING_CALLING]: {
    primary: "#10b981",
    secondary: "#047857",
    accent: "#34d399",
  },
  [AppType.IDENTITY_SSO]: {
    primary: "#8b5cf6",
    secondary: "#7c3aed",
    accent: "#a78bfa",
  },
  [AppType.EMAIL_CALENDARS]: {
    primary: "#f59e0b",
    secondary: "#d97706",
    accent: "#fbbf24",
  },
  [AppType.CLOUD_INFRASTRUCTURE]: {
    primary: "#06b6d4",
    secondary: "#0891b2",
    accent: "#22d3ee",
  },
  [AppType.DEV_COLLABORATION]: {
    primary: "#ef4444",
    secondary: "#dc2626",
    accent: "#f87171",
  },
  [AppType.SOCIAL_NETWORKING]: {
    primary: "#ec4899",
    secondary: "#db2777",
    accent: "#f472b6",
  },
  [AppType.OTP_APPS]: {
    primary: "#16a34a",
    secondary: "#15803d",
    accent: "#4ade80",
  },
};

export type YubikeySupport = {
  enabled: boolean;
  link?: string;
};

export type PhoneNumberRequirement = {
  enabled: boolean;
  link?: string;
  description?: string;
};

export type BackupSupport = {
  enabled: boolean;
  link?: string;
};

export type EmergencyLink = {
  link: string;
  description: string;
  label: string;
};

export type Instruction = {
  description: string;
  imageLink?: string;
};

export type Emergency = {
  links: EmergencyLink[];
  instructions: Instruction[];
};

export type App = {
  id: string;
  name: string;
  type: AppType;
  appLogo?: string;
  yubikeys: YubikeySupport;
  phoneNumber: PhoneNumberRequirement;
  backup: BackupSupport;
  healthAccount: string;
  emergency: Emergency;
  recommendations?: string;
  createdAt?: Date;
  updatedAt?: Date;
};
