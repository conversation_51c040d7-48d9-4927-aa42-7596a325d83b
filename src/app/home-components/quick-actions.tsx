"use client";

import React from "react";
import Link from "next/link";
import { Activity, AlertTriangle, ExternalLink, Zap } from "lucide-react";
import { App } from "@/core.constants";
import { Button } from "@/components/ui/button";

interface QuickActionsProps {
  app: App;
}

interface ActionButtonProps {
  href: string;
  icon: React.ElementType;
  title: string;
  subtitle: string;
  colorClass: string;
}

const ActionButton = ({
  href,
  icon: Icon,
  title,
  subtitle,
  colorClass,
}: ActionButtonProps) => (
  <Button
    asChild
    className={`w-full justify-start h-12 ${colorClass}`}
    variant="outline"
  >
    <Link href={href} target="_blank" className="flex items-center">
      <Icon className="w-4 h-4 mr-3" />
      <div className="text-left">
        <div className="font-medium">{title}</div>
        <div className="text-xs opacity-70">{subtitle}</div>
      </div>
      <ExternalLink className="w-4 h-4 ml-auto opacity-70" />
    </Link>
  </Button>
);

export const QuickActions = ({ app }: QuickActionsProps) => {
  const hasActions = app.healthAccount || app.emergency.links.length > 0;

  if (!hasActions) return null;

  return (
    <div>
      <div className="flex items-center space-x-2 mb-4">
        <Zap className="w-5 h-5 text-green-500" />
        <h2 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
          Quick Actions
        </h2>
      </div>
      <div className="space-y-3">
        {app.healthAccount && (
          <ActionButton
            href={app.healthAccount}
            icon={Activity}
            title="Security Health Check"
            subtitle="Review account security settings"
            colorClass="bg-blue-50 hover:bg-blue-100 dark:bg-blue-950 dark:hover:bg-blue-900 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800"
          />
        )}

        {app.emergency.links.length > 0 && (
          <ActionButton
            href={app.emergency.links[0].link}
            icon={AlertTriangle}
            title={`🚨 ${app.emergency.links[0].label}`}
            subtitle="Emergency account access"
            colorClass="bg-red-50 hover:bg-red-100 dark:bg-red-950 dark:hover:bg-red-900 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800"
          />
        )}
      </div>
    </div>
  );
};
