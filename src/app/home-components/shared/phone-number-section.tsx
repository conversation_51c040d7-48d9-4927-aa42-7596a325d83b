"use client";

import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Markdown } from "@/components/ui/markdown";
import { App } from "@/core.constants";
import { SectionHeader } from "./section-header";
import { Phone, ExternalLink } from "lucide-react";

interface PhoneNumberSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

export const PhoneNumberSection = ({
  app,
  variant = "drawer",
}: PhoneNumberSectionProps) => {
  if (!app.phoneNumber.description) return null;

  const isModal = variant === "modal";

  return (
    <div>
      <SectionHeader
        icon={Phone}
        title="Phone Number"
        iconColorClass={
          isModal
            ? "bg-gradient-to-r from-orange-500 to-red-500"
            : "text-orange-500"
        }
        variant={variant}
      />
      <div
        className={
          isModal
            ? "rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
            : "rounded-lg border border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800 p-4"
        }
      >
        <Markdown
          content={app.phoneNumber.description}
          className={
            isModal
              ? "text-neutral-300 mb-4"
              : "text-neutral-700 dark:text-neutral-300 mb-4"
          }
        />

        {app.phoneNumber.link && (
          <Button
            asChild
            className={
              isModal
                ? "w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-12"
                : "w-full justify-start h-12 bg-orange-500 hover:bg-orange-600 text-white"
            }
            variant={isModal ? "default" : "outline"}
          >
            <Link
              href={app.phoneNumber.link}
              target="_blank"
              className="flex items-center justify-center"
            >
              <Phone className="w-4 h-4 mr-3" />
              <span className="flex-1">
                {app.phoneNumber.enabled
                  ? "Remove Phone Number"
                  : "Manage Phone Number"}
              </span>
              <ExternalLink className="w-4 h-4 ml-3 opacity-70" />
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
};
