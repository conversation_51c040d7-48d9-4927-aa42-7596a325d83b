"use client";

import React, { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CollapsibleSectionProps {
  icon: React.ElementType;
  title: string;
  iconColorClass: string;
  variant?: "modal" | "drawer";
  children: React.ReactNode;
  defaultExpanded?: boolean;
}

export const CollapsibleSection = ({
  icon: Icon,
  title,
  iconColorClass,
  variant = "drawer",
  children,
  defaultExpanded = true,
}: CollapsibleSectionProps) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  if (variant === "modal") {
    return (
      <div>
        <Button
          variant="ghost"
          onClick={toggleExpanded}
          className="w-full p-0 h-auto hover:bg-transparent group"
        >
          <div className="flex items-center space-x-3 mb-4 w-full">
            <div
              className={`w-8 h-8 ${iconColorClass} rounded-lg flex items-center justify-center`}
            >
              <Icon className="w-3 h-3 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-white flex-1 text-left">{title}</h2>
            <div className="flex-1 h-px bg-gradient-to-r from-white/20 to-transparent" />
            {isExpanded ? (
              <ChevronUp className="w-5 h-5 text-white/70 group-hover:text-white transition-colors" />
            ) : (
              <ChevronDown className="w-5 h-5 text-white/70 group-hover:text-white transition-colors" />
            )}
          </div>
        </Button>
        {isExpanded && (
          <div className="animate-in slide-in-from-top-2 duration-200">
            {children}
          </div>
        )}
      </div>
    );
  }

  return (
    <div>
      <Button
        variant="ghost"
        onClick={toggleExpanded}
        className="w-full p-0 h-auto hover:bg-transparent group mb-4"
      >
        <div className="flex items-center space-x-2 w-full">
          <Icon className={`w-5 h-5 ${iconColorClass}`} />
          <h2 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 flex-1 text-left">
            {title}
          </h2>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-neutral-500 dark:text-neutral-400 group-hover:text-neutral-700 dark:group-hover:text-neutral-200 transition-colors" />
          ) : (
            <ChevronDown className="w-4 h-4 text-neutral-500 dark:text-neutral-400 group-hover:text-neutral-700 dark:group-hover:text-neutral-200 transition-colors" />
          )}
        </div>
      </Button>
      {isExpanded && (
        <div className="animate-in slide-in-from-top-2 duration-200">
          {children}
        </div>
      )}
    </div>
  );
};
