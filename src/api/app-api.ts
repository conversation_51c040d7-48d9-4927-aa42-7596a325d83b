import { App, AppType } from "@/core.constants";
import { firebaseStorage, firestore } from "@/root-context";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  DocumentSnapshot,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  updateDoc,
} from "firebase/firestore";
import {
  deleteObject,
  getDownloadURL,
  ref,
  uploadBytes,
} from "firebase/storage";

const COLLECTION_NAME = "apps";
const STORAGE_FOLDER = "apps";

export const createApp = async (
  appData: Omit<App, "id" | "createdAt" | "updatedAt">
) => {
  try {
    const docRef = await addDoc(collection(firestore, COLLECTION_NAME), {
      ...appData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  } catch (error) {
    console.error("Error creating app:", error);
    throw error;
  }
};

export const updateApp = async (id: string, appData: Partial<App>) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...appData,
      updatedAt: new Date(),
    });
  } catch (error) {
    console.error("Error updating app:", error);
    throw error;
  }
};

export const deleteApp = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting app:", error);
    throw error;
  }
};

export const getApps = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot
) => {
  try {
    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy("createdAt", "desc"),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy("createdAt", "desc"),
        startAfter(lastDoc),
        limit(pageSize)
      );
    }

    const snapshot = await getDocs(q);
    const apps: App[] = [];

    snapshot.forEach((doc) => {
      apps.push({ id: doc.id, ...doc.data() } as App);
    });

    return {
      apps,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error("Error fetching apps:", error);
    throw error;
  }
};

export const uploadInstructionImage = async (
  file: File,
  fileName: string
): Promise<string> => {
  try {
    const storageRef = ref(firebaseStorage, `${STORAGE_FOLDER}/${fileName}`);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error("Error uploading image:", error);
    throw error;
  }
};

export const uploadAppLogo = async (
  file: File,
  fileName: string
): Promise<string> => {
  try {
    const storageRef = ref(
      firebaseStorage,
      `${STORAGE_FOLDER}/logos/${fileName}`
    );
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error("Error uploading app logo:", error);
    throw error;
  }
};

export const deleteInstructionImage = async (imageUrl: string) => {
  try {
    const imageRef = ref(firebaseStorage, imageUrl);
    await deleteObject(imageRef);
  } catch (error) {
    console.error("Error deleting image:", error);
    throw error;
  }
};

export const seedOTPApps = async () => {
  try {
    const otpApps = [
      {
        name: "Google Authenticator",
        type: AppType.OTP_APPS,
        appLogo:
          "https://upload.wikimedia.org/wikipedia/commons/thumb/7/7a/Google_Authenticator_logo.svg/512px-Google_Authenticator_logo.svg.png",
        yubikeys: { enabled: false, link: "" },
        phoneNumber: {
          enabled: false,
          link: "",
          description:
            "Google Authenticator does not require a phone number for basic functionality. However, your Google account may require phone verification for account recovery.",
        },
        backup: {
          enabled: true,
          link: "https://support.google.com/accounts/answer/1066447",
        },
        healthAccount: "https://myaccount.google.com/security",
        emergency: {
          links: [
            {
              link: "https://support.google.com/accounts/answer/185834",
              description: "Recover your Google Account",
              label: "Account Recovery",
            },
          ],
          instructions: [
            {
              description:
                "Use backup codes to regain access to your account if you lose your phone or authenticator app.",
            },
          ],
        },
      },
      {
        name: "Authy",
        type: AppType.OTP_APPS,
        appLogo: "https://authy.com/wp-content/uploads/authy-logo-300x300.png",
        yubikeys: { enabled: false, link: "" },
        phoneNumber: {
          enabled: true,
          link: "https://help.twilio.com/articles/************",
          description:
            "**Authy requires a phone number** for account setup and verification. This is mandatory for creating and accessing your Authy account. You can remove your phone number later, but it's needed for initial setup and account recovery.",
        },
        backup: {
          enabled: true,
          link: "https://help.twilio.com/articles/************",
        },
        healthAccount: "https://authy.com/phones/",
        emergency: {
          links: [
            {
              link: "https://help.twilio.com/articles/************",
              description: "Recover Authy Account",
              label: "Account Recovery",
            },
          ],
          instructions: [
            {
              description:
                "Enable Authy backups to sync your tokens across devices and recover them if needed.",
            },
          ],
        },
      },
    ];

    const promises = otpApps.map((app) => createApp(app));
    await Promise.all(promises);

    return { success: true, message: "OTP apps seeded successfully" };
  } catch (error) {
    console.error("Error seeding OTP apps:", error);
    throw error;
  }
};

export const deleteAppLogo = async (imageUrl: string) => {
  try {
    const imageRef = ref(firebaseStorage, imageUrl);
    await deleteObject(imageRef);
  } catch (error) {
    console.error("Error deleting app logo:", error);
    throw error;
  }
};
